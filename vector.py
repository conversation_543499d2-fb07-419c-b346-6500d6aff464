# Vector store functionality for restaurant reviews

from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings
from langchain_core.documents import Document

import pandas as pd

def create_vector_store():
    """Create a Chroma vector store from restaurant reviews."""
    # Read the restaurant reviews data
    df = pd.read_csv('realistic_restaurant_reviews.csv')

    # Convert reviews to Document objects
    documents = []
    for index, row in df.iterrows():
        # Assuming the CSV has columns like 'review', 'rating', 'restaurant', etc.
        # Adjust column names based on your actual CSV structure
        doc = Document(
            page_content=str(row.get('review', row.iloc[0])),  # Use first column if 'review' doesn't exist
            metadata={
                'row_id': index,
                **{col: str(val) for col, val in row.items() if col != 'review'}
            }
        )
        documents.append(doc)

    # Initialize embeddings (requires OPENAI_API_KEY environment variable)
    embeddings = OpenAIEmbeddings()

    # Create Chroma vector store
    vector_store = Chroma.from_documents(
        documents=documents,
        embedding=embeddings,
        collection_name="restaurant_reviews",
        persist_directory="./chroma_db"
    )

    return vector_store

def search_reviews(query: str, k: int = 5):
    """Search for similar restaurant reviews."""
    # Initialize embeddings
    embeddings = OpenAIEmbeddings()

    # Load existing vector store
    vector_store = Chroma(
        collection_name="restaurant_reviews",
        embedding_function=embeddings,
        persist_directory="./chroma_db"
    )

    # Perform similarity search
    results = vector_store.similarity_search(query, k=k)
    return results

# Example usage
if __name__ == "__main__":
    # Create vector store (run once)
    # vector_store = create_vector_store()
    # print("Vector store created successfully!")

    # Search for reviews (example)
    # results = search_reviews("great food and service", k=3)
    # for i, doc in enumerate(results):
    #     print(f"Result {i+1}: {doc.page_content[:100]}...")
    pass